{"version": 3, "targets": {".NETStandard,Version=v2.1": {"SmartVertex.Tools.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/SmartVertex.Tools.Runtime.dll": {}}, "runtime": {"bin/placeholder/SmartVertex.Tools.Runtime.dll": {}}}}}, "libraries": {"SmartVertex.Tools.Runtime/1.0.0": {"type": "project", "path": "SmartVertex.Tools.Runtime.csproj", "msbuildProject": "SmartVertex.Tools.Runtime.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["SmartVertex.Tools.Runtime >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity\\Projects\\Packages\\SmartVertex.Tools.Editor.csproj", "projectName": "SmartVertex.Tools.Editor", "projectPath": "D:\\Unity\\Projects\\Packages\\SmartVertex.Tools.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity\\Projects\\Packages\\Temp\\obj\\SmartVertex.Tools.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity\\Projects\\Packages\\SmartVertex.Tools.Runtime.csproj": {"projectPath": "D:\\Unity\\Projects\\Packages\\SmartVertex.Tools.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.404\\RuntimeIdentifierGraph.json"}}}}