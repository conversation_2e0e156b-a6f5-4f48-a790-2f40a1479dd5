Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.0f1 (9ea152932a88) revision 10395986'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit EnterpriseS' Language: 'en' Physical Memory: 12214 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-05T08:59:23Z

COMMAND LINE ARGUMENTS:
D:\Unity\Editors\6000.1.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Unity/Projects/Packages
-logFile
Logs/AssetImportWorker0.log
-srvPort
12027
-job-worker-count
1
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/Unity/Projects/Packages
D:/Unity/Projects/Packages
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [13056]  Target information:

Player connection [13056]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3095878012 [EditorId] 3095878012 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13056]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 3095878012 [EditorId] 3095878012 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13056]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3095878012 [EditorId] 3095878012 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13056] Host joined multi-casting on [***********:54997]...
Player connection [13056] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 1
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 4.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.0f1 (9ea152932a88)
[Subsystems] Discovering subsystems at path D:/Unity/Editors/6000.1.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Unity/Projects/Packages/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        Intel(R) HD Graphics 620 (ID=0x5916)
    Vendor:          Intel
    VRAM:            6107 MB
    App VRAM Budget: 5546 MB
    Driver:          31.0.101.2130
    Unified Memory Architecture
    Cache Coherent UMA
Initialize mono
Mono path[0] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56880
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.003625 seconds.
- Loaded All Assemblies, in  1.111 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 3813 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.256 seconds
Domain Reload Profiling: 6367ms
	BeginReloadAssembly (486ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (80ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (108ms)
	LoadAllAssembliesAndSetupDomain (409ms)
		LoadAssemblies (486ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (398ms)
			TypeCache.Refresh (395ms)
				TypeCache.ScanAssembly (366ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (5258ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5150ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4056ms)
			SetLoadedEditorAssemblies (17ms)
			BeforeProcessingInitializeOnLoad (206ms)
			ProcessInitializeOnLoadAttributes (564ms)
			ProcessInitializeOnLoadMethodAttributes (307ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.057 seconds
Refreshing native plugins compatible for Editor in 3.64 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 10.615 seconds
Domain Reload Profiling: 12669ms
	BeginReloadAssembly (560ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (85ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (170ms)
	LoadAllAssembliesAndSetupDomain (1205ms)
		LoadAssemblies (984ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (557ms)
			TypeCache.Refresh (436ms)
				TypeCache.ScanAssembly (399ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (10616ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (9824ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (67ms)
			SetLoadedEditorAssemblies (21ms)
			BeforeProcessingInitializeOnLoad (311ms)
			ProcessInitializeOnLoadAttributes (9029ms)
			ProcessInitializeOnLoadMethodAttributes (380ms)
			AfterProcessingInitializeOnLoad (16ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.15 seconds
Refreshing native plugins compatible for Editor in 4.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 217 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6643 unused Assets / (4.8 MB). Loaded Objects now: 7358.
Memory consumption went from 166.4 MB to 161.6 MB.
Total: 62.246000 ms (FindLiveObjects: 3.830200 ms CreateObjectMapping: 5.490200 ms MarkObjects: 44.651900 ms  DeleteObjects: 8.271800 ms)

========================================================================
Received Import Request.
  Time since last request: 670825.338270 seconds.
  path: Assets/Tools/Runtime/Services
  artifactKey: Guid(303a53c5f8a6c374fbddbb7f938244c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/Services using Guid(303a53c5f8a6c374fbddbb7f938244c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'adc87ec58f5c7888f4e6761a3270f1df') in 0.0120977 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

